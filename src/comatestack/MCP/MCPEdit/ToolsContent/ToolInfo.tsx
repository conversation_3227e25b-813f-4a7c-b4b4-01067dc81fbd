import {Path} from '@panda-design/path-form';
import {Flex, Form, Input, Button} from 'antd';
import {DownOutlined, UpOutlined} from '@ant-design/icons';
import {useState} from 'react';
import {useMCPEditFormItem} from '../Providers/MCPEditFormItemProvider';

interface Props {
    path: Path;
}

const CollapsibleTextArea = ({value, onChange, placeholder, maxLength, showCount}: any) => {
    const [isExpanded, setIsExpanded] = useState(false);

    const toggleExpanded = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <div>
            <Flex justify="space-between" align="center" style={{marginBottom: 8}}>
                <span style={{fontSize: 12, color: '#666'}}>
                    {isExpanded ? '点击收起' : '点击展开查看完整内容'}
                </span>
                <Button
                    type="text"
                    size="small"
                    icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                    onClick={toggleExpanded}
                >
                    {isExpanded ? '收起' : '展开'}
                </Button>
            </Flex>
            {isExpanded && (
                <Input.TextArea
                    value={value}
                    onChange={onChange}
                    rows={3}
                    showCount={showCount}
                    maxLength={maxLength}
                    placeholder={placeholder}
                />
            )}
        </div>
    );
};

const ToolInfo = ({path}: Props) => {
    const {MCPEditFormItem} = useMCPEditFormItem();
    return (
        <>
            <Form.Item name={[...path, 'toolStatus']} hidden />
            <Form.Item name={[...path, 'toolKey']} hidden />
            <Form.Item name={[...path, 'id']} hidden />
            <Form.Item name={[...path, 'serverId']} hidden />
            <MCPEditFormItem
                name={[...path, 'name']}
                label="工具名称"
                rules={[{required: true, message: '请输入工具名称'}]}
            >
                <Input disabled={false} showCount maxLength={50} placeholder="请输入" />
            </MCPEditFormItem>
            <Flex gap={4}>
                <MCPEditFormItem
                    style={{flexGrow: 1}}
                    name={[...path, 'toolKey']}
                    label="工具标识"
                    rules={[{required: true, message: '请输入工具标识'}]}
                >
                    <Input showCount maxLength={50} placeholder="请输入" />
                </MCPEditFormItem>
                {/* <StyledButton type="text">一键生成</StyledButton> */}
            </Flex>
            <MCPEditFormItem
                name={[...path, 'description']}
                label="工具描述"
                rules={[{required: true, message: '请输入工具描述'}]}
            >
                <CollapsibleTextArea showCount maxLength={1000} placeholder="请输入" />
            </MCPEditFormItem>
        </>
    );
};

export default ToolInfo;
